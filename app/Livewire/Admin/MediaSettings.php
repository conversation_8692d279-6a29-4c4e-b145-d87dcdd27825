<?php

namespace App\Livewire\Admin;

use App\Models\Property;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Media Management'])]
class MediaSettings extends Component
{
    public $activeTab = 'conversions';
    public $isRegenerating = false;
    public $regenerationProgress = 0;
    public $regenerationTotal = 0;
    public $regenerationCurrent = 0;
    public $selectedProperties = [];
    public $selectAll = false;
    public $showBulkActions = false;
    public $operationMessage = '';
    public $operationType = 'info'; // info, success, error, warning

    protected $listeners = [
        'refreshMediaStats' => '$refresh',
        'regenerationComplete' => 'handleRegenerationComplete'
    ];

    public function mount()
    {
        $this->checkAdminAccess();
    }

    private function checkAdminAccess()
    {
        if (!Auth::user()?->hasRole('admin')) {
            abort(403, 'Access denied. Admin role required.');
        }
    }

    public function render()
    {
        $mediaStats = $this->getMediaStatistics();
        $queueStats = $this->getQueueStatistics();
        $properties = $this->getPropertiesWithMedia();
        
        return view('livewire.admin.media-settings', compact(
            'mediaStats',
            'queueStats', 
            'properties'
        ));
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetOperationMessage();
    }

    private function getMediaStatistics()
    {
        $totalMedia = Media::count();
        $mediaWithConversions = Media::whereJsonLength('generated_conversions', '>', 0)->count();
        $mediaWithoutConversions = $totalMedia - $mediaWithConversions;
        
        $conversionStats = Media::selectRaw('
            SUM(JSON_EXTRACT(generated_conversions, "$.thumb") = true) as thumb_count,
            SUM(JSON_EXTRACT(generated_conversions, "$.preview") = true) as preview_count
        ')->first();

        $storageSize = $this->calculateStorageSize();
        
        return [
            'total_media' => $totalMedia,
            'with_conversions' => $mediaWithConversions,
            'without_conversions' => $mediaWithoutConversions,
            'thumb_conversions' => $conversionStats->thumb_count ?? 0,
            'preview_conversions' => $conversionStats->preview_count ?? 0,
            'storage_size' => $storageSize,
            'conversion_success_rate' => $totalMedia > 0 ? round(($mediaWithConversions / $totalMedia) * 100, 1) : 0
        ];
    }

    private function getQueueStatistics()
    {
        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();
        
        // Get media conversion specific jobs
        $mediaJobs = DB::table('jobs')
            ->where('payload', 'like', '%PerformConversionsJob%')
            ->count();
            
        return [
            'pending_jobs' => $pendingJobs,
            'failed_jobs' => $failedJobs,
            'media_jobs' => $mediaJobs,
            'queue_status' => $this->getQueueWorkerStatus()
        ];
    }

    private function getQueueWorkerStatus()
    {
        // Simple check - in production you might want to use Horizon or Supervisor status
        $processes = shell_exec('ps aux | grep "queue:work" | grep -v grep | wc -l');
        return (int)$processes > 0 ? 'running' : 'stopped';
    }

    private function calculateStorageSize()
    {
        $totalSize = 0;
        $mediaFiles = Media::all();
        
        foreach ($mediaFiles as $media) {
            $path = $media->getPath();
            if (file_exists($path)) {
                $totalSize += filesize($path);
            }
            
            // Add conversion file sizes
            foreach (['thumb', 'preview'] as $conversion) {
                if ($media->hasGeneratedConversion($conversion)) {
                    $conversionPath = $media->getPath($conversion);
                    if (file_exists($conversionPath)) {
                        $totalSize += filesize($conversionPath);
                    }
                }
            }
        }
        
        return $this->formatBytes($totalSize);
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    private function getPropertiesWithMedia()
    {
        return Property::with(['media', 'user'])
            ->whereHas('media')
            ->orderBy('updated_at', 'desc')
            ->paginate(20);
    }

    public function regenerateAllConversions()
    {
        if ($this->isRegenerating) {
            return;
        }

        $this->isRegenerating = true;
        $this->regenerationProgress = 0;
        $this->regenerationTotal = Media::count();
        $this->regenerationCurrent = 0;
        
        Log::info('Admin initiated media conversion regeneration', [
            'user_id' => Auth::id(),
            'total_media' => $this->regenerationTotal
        ]);

        try {
            // Use Artisan command to regenerate conversions
            Artisan::call('media-library:regenerate', [
                '--force' => true
            ]);
            
            $this->setOperationMessage('All media conversions have been queued for regeneration.', 'success');
            
        } catch (\Exception $e) {
            Log::error('Failed to regenerate media conversions', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            $this->setOperationMessage('Failed to regenerate conversions: ' . $e->getMessage(), 'error');
        }
        
        $this->isRegenerating = false;
    }

    public function regenerateSelectedConversions()
    {
        if (empty($this->selectedProperties)) {
            $this->setOperationMessage('Please select properties to regenerate conversions for.', 'warning');
            return;
        }

        $properties = Property::whereIn('id', $this->selectedProperties)->with('media')->get();
        $mediaCount = 0;
        
        foreach ($properties as $property) {
            foreach ($property->media as $media) {
                $media->clearMediaConversions();
                $mediaCount++;
            }
        }
        
        Log::info('Admin initiated selective media conversion regeneration', [
            'user_id' => Auth::id(),
            'property_ids' => $this->selectedProperties,
            'media_count' => $mediaCount
        ]);

        $this->setOperationMessage("Regenerated conversions for {$mediaCount} media files from " . count($this->selectedProperties) . " properties.", 'success');
        $this->selectedProperties = [];
        $this->selectAll = false;
        $this->showBulkActions = false;
    }

    public function clearConversionCache()
    {
        try {
            // Clear Laravel cache
            Artisan::call('cache:clear');
            
            // Clear media conversion cache if exists
            if (Storage::disk('public')->exists('conversions')) {
                Storage::disk('public')->deleteDirectory('conversions');
            }
            
            Log::info('Admin cleared media conversion cache', [
                'user_id' => Auth::id()
            ]);
            
            $this->setOperationMessage('Media conversion cache cleared successfully.', 'success');
            
        } catch (\Exception $e) {
            Log::error('Failed to clear media conversion cache', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            $this->setOperationMessage('Failed to clear cache: ' . $e->getMessage(), 'error');
        }
    }

    public function processQueueJobs()
    {
        try {
            // Process queue jobs
            Artisan::call('queue:work', [
                '--once' => true,
                '--timeout' => 60
            ]);
            
            $this->setOperationMessage('Queue jobs processed successfully.', 'success');
            
        } catch (\Exception $e) {
            $this->setOperationMessage('Failed to process queue jobs: ' . $e->getMessage(), 'error');
        }
    }

    public function retryFailedJobs()
    {
        try {
            Artisan::call('queue:retry', ['--all' => true]);
            
            Log::info('Admin retried all failed queue jobs', [
                'user_id' => Auth::id()
            ]);
            
            $this->setOperationMessage('All failed jobs have been retried.', 'success');
            
        } catch (\Exception $e) {
            $this->setOperationMessage('Failed to retry jobs: ' . $e->getMessage(), 'error');
        }
    }

    public function clearFailedJobs()
    {
        try {
            Artisan::call('queue:flush');
            
            Log::info('Admin cleared all failed queue jobs', [
                'user_id' => Auth::id()
            ]);
            
            $this->setOperationMessage('All failed jobs have been cleared.', 'success');
            
        } catch (\Exception $e) {
            $this->setOperationMessage('Failed to clear failed jobs: ' . $e->getMessage(), 'error');
        }
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedProperties = $this->getPropertiesWithMedia()->pluck('id')->toArray();
        } else {
            $this->selectedProperties = [];
        }
        
        $this->showBulkActions = !empty($this->selectedProperties);
    }

    public function updatedSelectedProperties()
    {
        $this->showBulkActions = !empty($this->selectedProperties);
        $totalProperties = $this->getPropertiesWithMedia()->count();
        $this->selectAll = count($this->selectedProperties) === $totalProperties;
    }

    private function setOperationMessage($message, $type = 'info')
    {
        $this->operationMessage = $message;
        $this->operationType = $type;
    }

    private function resetOperationMessage()
    {
        $this->operationMessage = '';
        $this->operationType = 'info';
    }

    public function handleRegenerationComplete()
    {
        $this->isRegenerating = false;
        $this->regenerationProgress = 100;
        $this->setOperationMessage('Media conversion regeneration completed successfully.', 'success');
    }
}
